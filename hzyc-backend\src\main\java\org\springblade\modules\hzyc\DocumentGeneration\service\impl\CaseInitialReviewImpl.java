package org.springblade.modules.hzyc.DocumentGeneration.service.impl;

import org.springblade.modules.hzyc.DocumentGeneration.service.DocumentGenerator;
import org.springframework.stereotype.Service;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

import org.springblade.modules.hzyc.document.service.IDocumentService;
import org.springblade.modules.hzyc.document.pojo.entity.DocumentEntity;
import org.springblade.core.tool.jackson.JsonUtil;
import org.springframework.beans.factory.annotation.Autowired;

/**
 * 案件处理初审表文档生成实现类
 *
 * <AUTHOR>
 */
@Service("caseReviewFormDocument")
public class CaseInitialReviewImpl implements DocumentGenerator {

    @Autowired
    private IDocumentService documentService;

    @Override
    public List<Map<String, Object>> processData(Map<String, Object> rawData, String caseId, String mode, String fileId) {
        Map<String, Object> processedData = new HashMap<>(rawData);
        // 如果mode为update，从数据库读取已保存的数据
        if ("update".equals(mode) && caseId != null) {
            DocumentEntity existingDoc = documentService.getById(fileId);
            if (existingDoc != null && existingDoc.getDocumentContent() != null) {
                // 从JSON字符串解析为Map
                processedData = JsonUtil.readMap(existingDoc.getDocumentContent());
                return List.of(processedData);
            }
        }

        // 如果没有找到已保存的数据或mode不是update，使用模拟数据
        processedData = getMockData(); //todo，后续改为读取省局接口
        return List.of(processedData);
    }

    @Override
    public String getTemplateName() {
        return "26案件处理初审表.docx";
    }

    @Override
    public String getDocumentType() {
        return "CASE-INITIAL-REVIEW";
    }

    /**
     * 获取模拟数据
     */
    private Map<String, Object> getMockData() {
        Map<String, Object> mockData = new HashMap<>();

        // 基础信息
        mockData.put("case_uuid", "6358d676d24647f1b824c1f362674299");
        mockData.put("review_uuid", "51f8b0721d3c43fdb0adbc151c3a1489");
        mockData.put("org_name", "广东省博罗县烟草专卖局");
        mockData.put("case_no", "博烟案﹝2025﹞第48号");
        mockData.put("review_date", "2025/6/10");

        // 案件基本信息
        mockData.put("case_name", "梁俊强未在当地烟草专卖批发企业进货案");
        mockData.put("case_type", "行政处罚案件");
        mockData.put("case_source", "群众举报");
        mockData.put("case_date", "2025/3/18");
        mockData.put("case_addr", "广东省惠州市博罗县龙溪街道宫庭村龙桥大道1239号");

        // 当事人信息
        mockData.put("party_name", "梁俊强");
        mockData.put("party_type", "个体工商户");
        mockData.put("party_id_no", "441322199203166034");
        mockData.put("business_name", "博罗县龙溪隆胜轩茶烟酒商行");
        mockData.put("license_no", "************");
        mockData.put("contact_phone", "***********");

        // 违法事实概述
        mockData.put("violation_summary", "当事人梁俊强经营的博罗县龙溪隆胜轩茶烟酒商行，未在当地烟草专卖批发企业进货，现场发现涉嫌违法的烟草专卖品17个品种合计1075条。");

        // 涉案物品信息
        mockData.put("goods_count", "17个品种1075条");
        mockData.put("goods_value", "108625.00元");
        mockData.put("main_brands", "黄果树(长征)、白沙(硬精品三代)、红塔山(硬经典)等");

        // 证据材料清单
        mockData.put("evidence_list", "1、涉案卷烟17个品牌规格合计1075条；2、《证据先行登记保存通知书》1份；3、证据复制（提取）单1份；4、《询问笔录》1份；5、《现场笔录》1份；6、《卷烟、雪茄烟鉴别检验报告》1份；7、《涉案物品核价表》1份。");

        // 法律依据
        mockData.put("legal_basis", "《中华人民共和国烟草专卖法实施条例》第五十六条");

        // 初审意见
        mockData.put("review_opinion", "经初步审查，当事人梁俊强的行为违反了《中华人民共和国烟草专卖法实施条例》第二十三条第二款的规定，构成未在当地烟草专卖批发企业进货的违法行为。事实清楚，证据确凿，建议依法给予行政处罚。");

        // 处罚建议
        mockData.put("punishment_suggestion", "建议依据《中华人民共和国烟草专卖法实施条例》第五十六条的规定，对当事人处以进货总额9.5%的罚款，计罚款人民币10319.37元。");

        // 审查人员
        mockData.put("reviewer_name", "叶辉明");
        mockData.put("reviewer_id", "19090352015");
        mockData.put("reviewer_position", "专卖执法人员");

        // 审查结论
        mockData.put("review_conclusion", "同意");
        mockData.put("review_result", "建议移送审批");

        // 部门负责人意见
        mockData.put("dept_leader_opinion", "同意初审意见，建议按程序报批");
        mockData.put("dept_leader_name", "朱兆强");
        mockData.put("dept_leader_date", "2025/6/8");

        // 系统字段
        mockData.put("is_active", 1);
        mockData.put("creator", "叶辉明");
        mockData.put("create_time", "2025/6/8 14:30");
        mockData.put("modifier", "叶辉明");
        mockData.put("modify_time", "2025/6/8 14:30");
        mockData.put("own_dept_uuid", "4413231030000002829");
        mockData.put("own_org_uuid", "4413231030000000540");

        return mockData;
    }
}
