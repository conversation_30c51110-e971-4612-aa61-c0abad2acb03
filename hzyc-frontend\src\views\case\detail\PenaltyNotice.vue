<template>
  <div class="penalty-decision-container">
    <!-- 文档头部操作栏 -->
    <div class="document-header">
      <div class="header-left">
        <h3>行政处罚事先告知书</h3>
      </div>
      <div class="header-right">
        <el-button
          v-if="!isEditing"
          size="small"
          type="primary"
          @click="toggleEdit"
          icon="Edit">
          编辑
        </el-button>
        <template v-else>
          <el-button
            size="small"
            type="success"
            @click="saveEdit"
            icon="Check">
            保存
          </el-button>
          <el-button
            size="small"
            type="info"
            @click="cancelEdit"
            icon="Close">
            取消
          </el-button>
        </template>
        <el-button
          size="small"
          @click="downloadOriginalFile"
          icon="Download">
          导出
        </el-button>
      </div>
    </div>

    <!-- 直接显示DOCX预览，不使用弹窗 -->
    <div class="document-preview" v-if="props.fileUrl&&!isEditing">
      <div class="preview-content" v-loading="previewLoading">
        <VueOfficeDocx
          :src="props.fileUrl"
          style="width: 100%;"
          @rendered="onDocxRendered"
          @error="onPreviewError"
        />
      </div>
    </div>

    <!-- 如果没有文件URL或不是DOCX，显示表单编辑模式 -->
    <div v-else class="document-body">
      <template v-if="isEditing">
        <div class="document-layout">
          <!-- 文档头部 -->
          <div class="document-header-section">
            <div class="org-name">
              <el-input
                v-model="formData.org_shortname"
                placeholder="机构简称"
                class="org-input"
              />
            </div>

            <div class="document-title">
              <h2>行政处罚事先告知书</h2>
            </div>

            <div class="document-number">
              <el-input
                v-model="formData.full_doc_no"
                placeholder="文号"
                style="width: 200px;"
              />
            </div>
          </div>

          <!-- 当事人信息 -->
          <div class="content-section">
            <div class="textarea-wrapper">
              <el-input
                v-model="formData.party"
                type="textarea"
                :autosize="{ minRows: 2 }"
                placeholder="当事人信息"
                class="party-info auto-resize-textarea"
                maxlength="500"
                show-word-limit
              />
            </div>
          </div>

          <!-- 案件时间和案由 -->
          <div class="content-section">
            <div class="textarea-wrapper">
              <el-input
                v-model="formData.case_time"
                placeholder="案件时间"
                class="case-time"
                style="margin-bottom: 10px;"
              />
              <el-input
                v-model="formData.cause_of_action"
                placeholder="案由"
                class="cause-of-action"
              />
            </div>
          </div>

          <!-- 法律依据 -->
          <div class="content-section">
            <div class="textarea-wrapper">
              <el-input
                v-model="formData.legal_argument"
                type="textarea"
                :autosize="{ minRows: 3 }"
                placeholder="法律依据"
                class="legal-argument auto-resize-textarea"
                maxlength="1000"
                show-word-limit
              />
            </div>
          </div>

          <!-- 处罚依据 -->
          <div class="content-section">
            <div class="textarea-wrapper">
              <el-input
                v-model="formData.punish_argument"
                type="textarea"
                :autosize="{ minRows: 3 }"
                placeholder="处罚依据"
                class="punish-argument auto-resize-textarea"
                maxlength="1000"
                show-word-limit
              />
            </div>
          </div>

          <!-- 处罚信息 -->
          <div class="content-section">
            <div class="textarea-wrapper">
              <el-input
                v-model="formData.punish_info"
                type="textarea"
                :autosize="{ minRows: 4 }"
                placeholder="处罚信息"
                class="punish-info auto-resize-textarea"
                maxlength="2000"
                show-word-limit
              />
            </div>
          </div>

          <!-- 机构信息 -->
          <div class="content-section">
            <div class="textarea-wrapper">
              <el-input
                v-model="formData.org_addr"
                placeholder="机构地址"
                class="org-addr"
                style="margin-bottom: 10px;"
              />
              <el-input
                v-model="formData.org_tel"
                placeholder="联系电话"
                class="org-tel"
              />
            </div>
          </div>

          <!-- 签名区域 -->
          <div class="signature-section">
            <div class="signature-line">
              <span>落款（印章）</span>
            </div>
            <div class="date-line">
              <el-input
                v-model="formData.sys_create_time"
                placeholder="日期"
                style="width: 200px;"
              />
            </div>
          </div>
        </div>
      </template>

    </div>
  </div>
</template>

<script setup>
import { ref, watch, onMounted, onUnmounted } from 'vue'
import { ElMessage } from 'element-plus'
import { Edit, Check, View, Download, FullScreen, ScaleToOriginal, Close } from '@element-plus/icons-vue'
import VueOfficeDocx from '@vue-office/docx'
// 在组件中导入样式
import '@/styles/vue-office-docx.css'
import '@/styles/document-common.scss';

const props = defineProps({
  documentData: {
    type: Object,
    default: () => ({})
  },
  fileUrl: {
    type: String,
    default: ''
  },
  fileType: {
    type: String,
    default: ''
  },
  fileName: {
    type: String,
    default: ''
  }
})

// 添加调试日志
watch(() => props.fileUrl, (newVal, oldVal) => {
  console.log('fileUrl changed:', { newVal, oldVal })
}, { immediate: true })

watch(() => props, (newVal) => {
  console.log('所有props:', newVal)
}, { immediate: true, deep: true })

const emit = defineEmits(['save'])

// 编辑状态
const isEditing = ref(false)

// 表单数据
const formData = ref({
  full_doc_no:'',
  party: '',
  case_time: '',
  cause_of_action: '',
  legal_argument: '',
  punish_argument: '',
  punish_info: '',
  org_shortname: '',
  org_addr: '',
  org_tel: '',
  sys_create_time: ''
})

// 预览相关状态
const previewLoading = ref(false)

// 保存原始数据的备份
const originalFormData = ref({})

// 监听传入的文档数据
watch(() => props.documentData, (newVal) => {
  if (newVal && Object.keys(newVal).length > 0) {
    // 从documentContent中提取属性，先解析JSON字符串
    let docContent = {}
    try {
      if (typeof newVal.documentContent === 'string') {
        docContent = JSON.parse(newVal.documentContent)
      } else {
        docContent = newVal.documentContent || {}
      }
    } catch (error) {
      docContent = {}
    }

    // 合并数据，优先使用documentContent中的数据
    formData.value = {
      ...formData.value,
      ...newVal,
      // 从documentContent中提取具体字段
      party: docContent.party || newVal.party || '',
      case_time: docContent.case_time || newVal.case_time || '',
      cause_of_action: docContent.cause_of_action || newVal.cause_of_action || '',
      legal_argument: docContent.legal_argument || newVal.legal_argument || '',
      punish_argument: docContent.punish_argument || newVal.punish_argument || '',
      punish_info: docContent.punish_info || newVal.punish_info || '',
      org_shortname: docContent.org_shortname || newVal.org_shortname || '',
      org_addr: docContent.org_addr || newVal.org_addr || '',
      org_tel: docContent.org_tel || newVal.org_tel || '',
      sys_create_time: docContent.sys_create_time || newVal.sys_create_time || '',
      full_doc_no: docContent.full_doc_no || newVal.full_doc_no || ''
    }

    // 保存原始数据的备份
    originalFormData.value = { ...formData.value }
  }
}, { immediate: true, deep: true })

// 修改切换编辑状态函数
const toggleEdit = () => {
  if (!isEditing.value) {
    // 进入编辑模式时保存当前数据作为备份
    originalFormData.value = { ...formData.value }
  }
  isEditing.value = !isEditing.value
}

// 新增保存编辑函数
const saveEdit = () => {
  // 保存时只传递需要的数据字段，避免传递整个formData
  const saveData = {
    party: formData.value.party,
    case_time: formData.value.case_time,
    cause_of_action: formData.value.cause_of_action,
    legal_argument: formData.value.legal_argument,
    punish_argument: formData.value.punish_argument,
    punish_info: formData.value.punish_info,
    org_shortname: formData.value.org_shortname,
    org_addr: formData.value.org_addr,
    org_tel: formData.value.org_tel,
    sys_create_time: formData.value.sys_create_time,
    full_doc_no: formData.value.full_doc_no,
    bureauName: formData.value.bureauName,
    documentPrefix: formData.value.documentPrefix,
    documentYear: formData.value.documentYear,
    documentNumber: formData.value.documentNumber
  }

  emit('save', saveData)
  isEditing.value = false
}

// 新增取消编辑函数
const cancelEdit = () => {
  // 恢复原始数据
  formData.value = { ...originalFormData.value }
  isEditing.value = false
  ElMessage.info('已取消编辑，数据已恢复')
}

// 文档渲染完成回调
const onDocxRendered = () => {
  previewLoading.value = false
}

// 预览错误处理
const onPreviewError = (error) => {
  previewLoading.value = false
  console.error('文档预览失败:', error)
  ElMessage.error('文档预览失败，请检查文件格式或网络连接')
}

// 下载原文件
const downloadOriginalFile = () => {
  if (props.fileUrl) {
    const link = document.createElement('a')
    link.href = props.fileUrl
    link.download = props.fileName || '文档'
    link.target = '_blank'
    document.body.appendChild(link)
    link.click()
    document.body.removeChild(link)
    ElMessage.success('开始下载原文件')
  } else {
    ElMessage.warning('原文件下载链接不存在')
  }
}

// 在 onMounted 中添加事件监听
onMounted(() => {
  // 监听优化事件
  const handleOptimizeEvent = (event) => {
    const { action } = event.detail
    // 根据不同的优化动作进入编辑模式
    if (action === 'partyInfo' || action === 'lawBasis' || action === 'penalty') {
      isEditing.value = true
      ElMessage.info('已进入编辑模式，请完善相关信息')
    }
  }

  document.addEventListener('document-optimize', handleOptimizeEvent)

  // 组件卸载时移除监听器
  onUnmounted(() => {
    document.removeEventListener('document-optimize', handleOptimizeEvent)
  })
})
</script>

<style scoped>
/* 组件特有样式已移至 @/styles/document-common.scss */
</style>
