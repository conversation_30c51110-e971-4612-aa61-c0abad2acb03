<template>
  <div class="investigation-report-container">
    <!-- 文档头部操作栏 -->
    <div class="document-header">
      <div class="header-left">
        <h3>案件调查终结报告</h3>
      </div>
      <div class="header-right">
        <el-button
          v-if="!isEditing"
          size="small"
          type="primary"
          @click="toggleEdit"
          icon="Edit">
          编辑
        </el-button>
        <template v-else>
          <el-button
            size="small"
            type="success"
            @click="saveEdit"
            icon="Check">
            保存
          </el-button>
          <el-button
            size="small"
            type="info"
            @click="cancelEdit"
            icon="Close">
            取消
          </el-button>
        </template>
        <el-button
          size="small"
          @click="downloadOriginalFile"
          icon="Download">
          导出
        </el-button>
      </div>
    </div>

    <!-- 直接显示DOCX预览，不使用弹窗 -->
    <div class="document-preview" v-if="props.fileUrl&&!isEditing">
      <div class="preview-content" v-loading="previewLoading">
        <VueOfficeDocx
          :src="props.fileUrl"
          style="width: 100%;"
          @rendered="onDocxRendered"
          @error="onPreviewError"
        />
      </div>
    </div>

    <!-- 如果没有文件URL或不是DOCX，显示表单编辑模式 -->
    <div v-else class="document-body">
      <template v-if="isEditing">
        <div class="document-layout">
          <!-- 文档头部 -->
          <div class="document-header-section">
            <div class="org-name">
              <el-input
                v-model="formData.pic_person"
                placeholder="当事人"
                class="org-input"
              />
            </div>

            <div class="document-title">
              <h2>案件调查终结报告</h2>
            </div>
          </div>

          <!-- 报告表格 -->
          <div class="form-table">
            <table class="delivery-table">
              <!-- 案由 -->
              <tr>
                <td class="label-cell" rowspan="2">案由</td>
                <td class="content-cell">
                  <el-input
                    v-model="formData.cause_of_action"
                    type="textarea"
                    :autosize="{ minRows: 2 }"
                    placeholder="案由"
                    class="auto-resize-textarea"
                    maxlength="500"
                    show-word-limit
                  />
                </td>
              </tr>

              <!-- 案件来源和立案日期 -->
              <tr>
                <td class="content-cell">
                  <div style="display: flex; align-items: center; gap: 20px;">
                    <div style="display: flex; align-items: center; gap: 10px;">
                      <span>案件来源</span>
                      <el-input
                        v-model="formData.case_from"
                        placeholder="案件来源"
                        style="width: 200px;"
                      />
                    </div>
                    <div style="display: flex; align-items: center; gap: 10px;">
                      <span>立案日期</span>
                      <el-input
                        v-model="formData.reg_date"
                        placeholder="立案日期"
                        style="width: 200px;"
                      />
                    </div>
                  </div>
                </td>
              </tr>

              <!-- 当事人信息 -->
              <tr>
                <td class="label-cell" rowspan="3">个人（个体工商户）</td>
                <td class="content-cell">
                  <table style="width: 100%; border-collapse: collapse;">
                    <tr>
                      <td style="border: 1px solid #333; padding: 8px; background-color: #f9f9f9; text-align: center; width: 80px;">姓名</td>
                      <td style="border: 1px solid #333; padding: 8px; width: 120px;">
                        <el-input
                          v-model="formData.party"
                          placeholder="姓名"
                          size="small"
                        />
                      </td>
                      <td style="border: 1px solid #333; padding: 8px; background-color: #f9f9f9; text-align: center; width: 80px;">性别</td>
                      <td style="border: 1px solid #333; padding: 8px; width: 80px;">
                        <el-input
                          v-model="formData.sex"
                          placeholder="性别"
                          size="small"
                        />
                      </td>
                      <td style="border: 1px solid #333; padding: 8px; background-color: #f9f9f9; text-align: center; width: 80px;">年龄</td>
                      <td style="border: 1px solid #333; padding: 8px; width: 80px;">
                        <el-input
                          v-model="formData.age"
                          placeholder="年龄"
                          size="small"
                        />
                      </td>
                      <td style="border: 1px solid #333; padding: 8px; background-color: #f9f9f9; text-align: center; width: 80px;">民族</td>
                      <td style="border: 1px solid #333; padding: 8px;">
                        <el-input
                          v-model="formData.nation"
                          placeholder="民族"
                          size="small"
                        />
                      </td>
                    </tr>
                  </table>
                </td>
              </tr>

              <tr>
                <td class="content-cell">
                  <table style="width: 100%; border-collapse: collapse;">
                    <tr>
                      <td style="border: 1px solid #333; padding: 8px; background-color: #f9f9f9; text-align: center; width: 120px;">证件类型及号码</td>
                      <td style="border: 1px solid #333; padding: 8px;" colspan="2">
                        <el-input
                          v-model="formData.id_card_type"
                          placeholder="证件类型"
                          size="small"
                          style="width: 120px; margin-right: 10px;"
                        />
                        <el-input
                          v-model="formData.id_card"
                          placeholder="证件号码"
                          size="small"
                          style="width: 200px;"
                        />
                      </td>
                      <td style="border: 1px solid #333; padding: 8px; background-color: #f9f9f9; text-align: center; width: 80px;">联系电话</td>
                      <td style="border: 1px solid #333; padding: 8px;" colspan="3">
                        <el-input
                          v-model="formData.phone"
                          placeholder="联系电话"
                          size="small"
                        />
                      </td>
                    </tr>
                  </table>
                </td>
              </tr>

              <tr>
                <td class="content-cell">
                  <table style="width: 100%; border-collapse: collapse;">
                    <tr>
                      <td style="border: 1px solid #333; padding: 8px; background-color: #f9f9f9; text-align: center; width: 80px;">住址</td>
                      <td style="border: 1px solid #333; padding: 8px;">
                        <el-input
                          v-model="formData.address"
                          placeholder="住址"
                          size="small"
                        />
                      </td>
                    </tr>
                  </table>
                </td>
              </tr>

              <!-- 调查人 -->
              <tr>
                <td class="label-cell">调查人</td>
                <td class="content-cell">
                  <el-input
                    v-model="formData.undertaker"
                    type="textarea"
                    :autosize="{ minRows: 2 }"
                    placeholder="调查人"
                    class="auto-resize-textarea"
                    maxlength="200"
                    show-word-limit
                  />
                </td>
              </tr>

              <!-- 调查事实 -->
              <tr>
                <td class="label-cell">调查事实</td>
                <td class="content-cell">
                  <el-input
                    v-model="formData.invstg_content"
                    type="textarea"
                    :autosize="{ minRows: 4 }"
                    placeholder="调查事实"
                    class="auto-resize-textarea"
                    maxlength="2000"
                    show-word-limit
                  />
                </td>
              </tr>

              <!-- 案件性质 -->
              <tr>
                <td class="label-cell">案件性质</td>
                <td class="content-cell">
                  <el-input
                    v-model="formData.case_property"
                    type="textarea"
                    :autosize="{ minRows: 3 }"
                    placeholder="案件性质"
                    class="auto-resize-textarea"
                    maxlength="1500"
                    show-word-limit
                  />
                </td>
              </tr>

              <!-- 处罚依据 -->
              <tr>
                <td class="label-cell">处罚（处理）依据</td>
                <td class="content-cell">
                  <el-input
                    v-model="formData.punish_argument"
                    type="textarea"
                    :autosize="{ minRows: 3 }"
                    placeholder="处罚（处理）依据"
                    class="auto-resize-textarea"
                    maxlength="1500"
                    show-word-limit
                  />
                </td>
              </tr>

              <!-- 处理意见 -->
              <tr>
                <td class="label-cell">处理意见</td>
                <td class="content-cell">
                  <div class="opinion-content">
                    <el-input
                      v-model="formData.handle_advice"
                      type="textarea"
                      :autosize="{ minRows: 4 }"
                      placeholder="处理意见"
                      class="auto-resize-textarea"
                      maxlength="2000"
                      show-word-limit
                    />
                    <div class="signature-section">
                      <div class="signature-line">
                        <span>承办人签名：</span>
                        <el-input
                          v-model="formData.undertaker"
                          placeholder="承办人"
                          style="width: 150px; margin: 0 10px;"
                        />
                        <el-input
                          v-model="formData.rpt_date"
                          placeholder="日期"
                          style="width: 200px;"
                        />
                      </div>
                    </div>
                  </div>
                </td>
              </tr>

              <!-- 备注 -->
              <tr>
                <td class="label-cell">备注</td>
                <td class="content-cell">
                  <el-input
                    v-model="formData.remarks"
                    type="textarea"
                    :autosize="{ minRows: 2 }"
                    placeholder="备注"
                    class="auto-resize-textarea"
                    maxlength="500"
                    show-word-limit
                  />
                </td>
              </tr>
            </table>
          </div>
        </div>
      </template>

    </div>
  </div>
</template>

<script setup>
import { ref, watch, onMounted, onUnmounted } from 'vue'
import { ElMessage } from 'element-plus'
import { Edit, Check, View, Download, FullScreen, ScaleToOriginal, Close } from '@element-plus/icons-vue'
import VueOfficeDocx from '@vue-office/docx'
// 在组件中导入样式
import '@/styles/vue-office-docx.css'
import '@/styles/document-common.scss';

const props = defineProps({
  documentData: {
    type: Object,
    default: () => ({})
  },
  fileUrl: {
    type: String,
    default: ''
  },
  fileType: {
    type: String,
    default: ''
  },
  fileName: {
    type: String,
    default: ''
  }
})

// 添加调试日志
watch(() => props.fileUrl, (newVal, oldVal) => {
  console.log('fileUrl changed:', { newVal, oldVal })
}, { immediate: true })

watch(() => props, (newVal) => {
  console.log('所有props:', newVal)
}, { immediate: true, deep: true })

const emit = defineEmits(['save'])

// 编辑状态
const isEditing = ref(false)

// 表单数据
const formData = ref({
  pic_person: '',
  cause_of_action: '',
  case_from: '',
  reg_date: '',
  party: '',
  sex: '',
  age: '',
  nation: '',
  id_card_type: '',
  id_card: '',
  phone: '',
  address: '',
  undertaker: '',
  invstg_content: '',
  case_property: '',
  punish_argument: '',
  handle_advice: '',
  rpt_date: '',
  remarks: ''
})

// 预览相关状态
const previewDialogVisible = ref(false)
const previewLoading = ref(false)
const isFullscreen = ref(false)

// 保存原始数据的备份
const originalFormData = ref({})

// 监听传入的文档数据
watch(() => props.documentData, (newVal) => {
  if (newVal && Object.keys(newVal).length > 0) {
    // 从documentContent中提取属性，先解析JSON字符串
    let docContent = {}
    try {
      if (typeof newVal.documentContent === 'string') {
        docContent = JSON.parse(newVal.documentContent)
      } else {
        docContent = newVal.documentContent || {}
      }
    } catch (error) {
      docContent = {}
    }

    // 合并数据，优先使用documentContent中的数据
    formData.value = {
      ...formData.value,
      ...newVal,
      // 从documentContent中提取具体字段
      pic_person: docContent.pic_person || newVal.pic_person || '',
      cause_of_action: docContent.cause_of_action || newVal.cause_of_action || '',
      case_from: docContent.case_from || newVal.case_from || '',
      reg_date: docContent.reg_date || newVal.reg_date || '',
      party: docContent.party || newVal.party || '',
      sex: docContent.sex || newVal.sex || '',
      age: docContent.age || newVal.age || '',
      nation: docContent.nation || newVal.nation || '',
      id_card_type: docContent.id_card_type || newVal.id_card_type || '',
      id_card: docContent.id_card || newVal.id_card || '',
      phone: docContent.phone || newVal.phone || '',
      address: docContent.address || newVal.address || '',
      undertaker: docContent.undertaker || newVal.undertaker || '',
      invstg_content: docContent.invstg_content || newVal.invstg_content || '',
      case_property: docContent.case_property || newVal.case_property || '',
      punish_argument: docContent.punish_argument || newVal.punish_argument || '',
      handle_advice: docContent.handle_advice || newVal.handle_advice || '',
      rpt_date: docContent.rpt_date || newVal.rpt_date || '',
      remarks: docContent.remarks || newVal.remarks || ''
    }

    // 保存原始数据的备份
    originalFormData.value = { ...formData.value }
  }
}, { immediate: true, deep: true })

// 修改切换编辑状态函数
const toggleEdit = () => {
  if (!isEditing.value) {
    // 进入编辑模式时保存当前数据作为备份
    originalFormData.value = { ...formData.value }
  }
  isEditing.value = !isEditing.value
}

// 新增保存编辑函数
const saveEdit = () => {
  // 保存时只传递需要的数据字段，避免传递整个formData
  const saveData = {
    pic_person: formData.value.pic_person,
    cause_of_action: formData.value.cause_of_action,
    case_from: formData.value.case_from,
    reg_date: formData.value.reg_date,
    party: formData.value.party,
    sex: formData.value.sex,
    age: formData.value.age,
    nation: formData.value.nation,
    id_card_type: formData.value.id_card_type,
    id_card: formData.value.id_card,
    phone: formData.value.phone,
    address: formData.value.address,
    undertaker: formData.value.undertaker,
    invstg_content: formData.value.invstg_content,
    case_property: formData.value.case_property,
    punish_argument: formData.value.punish_argument,
    handle_advice: formData.value.handle_advice,
    rpt_date: formData.value.rpt_date,
    remarks: formData.value.remarks
  }

  emit('save', saveData)
  isEditing.value = false
}

// 新增取消编辑函数
const cancelEdit = () => {
  // 恢复原始数据
  formData.value = { ...originalFormData.value }
  isEditing.value = false
  ElMessage.info('已取消编辑，数据已恢复')
}

// 文档渲染完成回调
const onDocxRendered = () => {
  previewLoading.value = false
}

// 预览错误处理
const onPreviewError = (error) => {
  previewLoading.value = false
  console.error('文档预览失败:', error)
  ElMessage.error('文档预览失败，请检查文件格式或网络连接')
}

// 下载原文件
const downloadOriginalFile = () => {
  if (props.fileUrl) {
    const link = document.createElement('a')
    link.href = props.fileUrl
    link.download = props.fileName || '文档'
    link.target = '_blank'
    document.body.appendChild(link)
    link.click()
    document.body.removeChild(link)
    ElMessage.success('开始下载原文件')
  } else {
    ElMessage.warning('原文件下载链接不存在')
  }
}

// 在 onMounted 中添加事件监听
onMounted(() => {
  // 监听优化事件
  const handleOptimizeEvent = (event) => {
    const { action } = event.detail
    // 根据不同的优化动作进入编辑模式
    if (action === 'partyInfo' || action === 'lawBasis' || action === 'penalty') {
      isEditing.value = true
      ElMessage.info('已进入编辑模式，请完善相关信息')
    }
  }

  document.addEventListener('document-optimize', handleOptimizeEvent)

  // 组件卸载时移除监听器
  onUnmounted(() => {
    document.removeEventListener('document-optimize', handleOptimizeEvent)
  })
})
</script>

<style scoped>
/* 组件特有样式已移至 @/styles/document-common.scss */
</style>
