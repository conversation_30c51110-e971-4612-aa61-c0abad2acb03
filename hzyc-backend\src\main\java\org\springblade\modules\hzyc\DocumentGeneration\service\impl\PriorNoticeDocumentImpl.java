package org.springblade.modules.hzyc.DocumentGeneration.service.impl;

import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONArray;
import org.springblade.modules.hzyc.DocumentGeneration.service.DocumentGenerator;
import org.springblade.modules.hzyc.DocumentGeneration.service.ICaseInfoService;
import org.springframework.stereotype.Service;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

import org.springblade.modules.hzyc.document.service.IDocumentService;
import org.springblade.modules.hzyc.document.pojo.entity.DocumentEntity;
import org.springblade.core.tool.jackson.JsonUtil;
import org.springframework.beans.factory.annotation.Autowired;

/**
 * 行政处罚事先告知书文档生成实现类
 *
 * <AUTHOR>
 */
@Service("penaltyNoticeDocument")
public class PriorNoticeDocumentImpl implements DocumentGenerator {

    @Autowired
    private IDocumentService documentService;
	@Autowired
	private ICaseInfoService icaseInfoService;

    @Override
    public List<Map<String, Object>> processData(Map<String, Object> rawData, String caseId, String mode, String fileId) {
        Map<String, Object> processedData = new HashMap<>(rawData);
        // 如果mode为update，从数据库读取已保存的数据
        if ("update".equals(mode) && caseId != null) {
            DocumentEntity existingDoc = documentService.getById(fileId);
            if (existingDoc != null && existingDoc.getDocumentContent() != null) {
                // 从JSON字符串解析为Map
                processedData = JsonUtil.readMap(existingDoc.getDocumentContent());
                return List.of(processedData);
            }
        }

        // 如果没有找到已保存的数据或mode不是update，使用模拟数据
		processedData = StrUtil.isNotBlank(caseId) ? getData(caseId) : getMockData();
        return List.of(processedData);
    }

    @Override
    public String getTemplateName() {
        return "27行政处罚事先告知书.docx";
    }

    @Override
    public String getDocumentType() {
        return "PRIOR-NOTICE";
    }

	private Map<String, Object> getData(String caseId) {
		Map<String, Object> data = new HashMap<>();
		Map<String, Object> query=new HashMap<>();
		query.put("AJBS", caseId);
		JSONArray array = icaseInfoService.getAdminPenaltyNoticeDailyReport(query);
		if(array != null && !array.isEmpty()) {
			Map<String, Object> firstData = (Map<String, Object>) array.get(0);
			Map<String, String> mapper = getTableFieldMapping();
			if(firstData != null) {
				// 处理数据
				firstData.forEach((key, value) -> {
					String newKey = mapper.get(key);
					if (StrUtil.isBlank(newKey)) {
						newKey = key;
					}
					data.put(newKey, value);
				});
				System.out.println(data);
				return data;
			}
		}
		return data;
	}

	public static Map<String, String> getTableFieldMapping() {
		Map<String, String> fieldMap = new HashMap<>();

		fieldMap.put("AJBH", "case_code");
		fieldMap.put("GXSJ", "dc_tec_utime");
		fieldMap.put("XTGXSJCXBYDX", "sys_modify_time");
		fieldMap.put("WSH", "doc_no");
		fieldMap.put("AJMC", "case_name");
		fieldMap.put("CFYJ", "punish_argument");
		fieldMap.put("CHBMUUID", "get_dept_uuid");
		fieldMap.put("WFSS", "legal_fact");
		fieldMap.put("SXGZSBS", "punish_notice_uuid");
		fieldMap.put("MCRKSJ", "mc_tec_ctime");
		fieldMap.put("DSRYJ", "party_advice");
		fieldMap.put("SJBM", "city_org_code");
		fieldMap.put("DWJC", "org_shortname");
		fieldMap.put("WFXWCD", "bcb_enum_legal_level");
		fieldMap.put("SJSSDWYYTYSJQXCL", "own_org_uuid");
		fieldMap.put("CJSJ", "create_time");
		fieldMap.put("XGSJ", "modify_time");
		fieldMap.put("ZFBMDH", "org_tel");
		fieldMap.put("SDFS", "service_style");
		fieldMap.put("DWDZ", "org_addr_street");
		fieldMap.put("BZ", "remark");
		fieldMap.put("AJXZ", "cause_of_action");
		fieldMap.put("XGR", "modifier");
		fieldMap.put("DWDHLXHM", "org_phone_no");
		fieldMap.put("ZLBS", "dc_tec_operation");
		fieldMap.put("DWSXZ", "org_abbr");
		fieldMap.put("KZZD1", "ext1");
		fieldMap.put("AJBS", "case_uuid");
		fieldMap.put("SFYX", "is_active");
		fieldMap.put("ZZJGUUID", "org_uuid");
		fieldMap.put("ND", "doc_year");
		fieldMap.put("YB", "post_code");
		fieldMap.put("RKSJ", "dc_tec_ctime");
		fieldMap.put("FWZXZDTBSJYFWZXSJS", "sysisdelete");
		fieldMap.put("WFTK", "legal_argument");
		fieldMap.put("ZJNR", "evide_content");
		fieldMap.put("WSHQ", "full_doc_no");
		fieldMap.put("KZZD2", "ext2");
		fieldMap.put("FWZXZDTBSJYFWZXSJG", "sysupdatetime");
		fieldMap.put("SJSSBMYYTYSJQXCL", "own_dept_uuid");
		fieldMap.put("SJMC", "city_org_name");
		fieldMap.put("SDDD", "service_addr");
		fieldMap.put("CJR", "creator");
		fieldMap.put("LXR", "contact_person");
		fieldMap.put("XYWYBS", "tid");
		fieldMap.put("CSSBQX", "excuse_limit_day");
		fieldMap.put("AFSJ", "case_time");
		fieldMap.put("TAR", "same_party");
		fieldMap.put("KZZD3", "ext3");
		fieldMap.put("CFXX", "punish_info");
		fieldMap.put("ZFBM", "org_addr");
		fieldMap.put("WSRQ", "doc_date");
		fieldMap.put("CBBMUUID", "reg_dept_uuid");
		fieldMap.put("CBRUUIDS", "undertaker_uuids");
		fieldMap.put("DSR", "party");
		fieldMap.put("CBR", "undertaker");
		fieldMap.put("SFLX0F1S", "finsh_status");
		fieldMap.put("XTCJSJCXBYDX", "sys_create_time");

		return fieldMap;
	}

    /**
     * 获取模拟数据
     */
    private Map<String, Object> getMockData() {
        Map<String, Object> mockData = new HashMap<>();

        // 基础信息
        mockData.put("notice_uuid", "51f8b0721d3c43fdb0adbc151c3a1489");
        mockData.put("case_uuid", "6358d676d24647f1b824c1f362674299");
        mockData.put("org_name", "广东省博罗县烟草专卖局");
        mockData.put("notice_no", "博烟告﹝2025﹞第48号");
        mockData.put("notice_date", "2025/6/9");

        // 当事人信息
        mockData.put("party_name", "梁俊强");
        mockData.put("party_info", "当事人：梁俊强，字号：博罗县龙溪隆胜轩茶烟酒商行，性别：男性，民族：汉族，职业：个体工商户，身份证住址：广东省博罗县龙溪街道长湖村合湖小组193号，居民身份证号码：441322199203166034，烟草专卖零售许可证号：************，统一社会信用代码：92441322MA56B9KR4D，经营地址：广东省博罗县龙溪街道宫庭村龙桥大道1239号，联系电话：13640736270。");

        // 违法事实
        mockData.put("violation_facts", "经我局调查，你（单位）存在以下违法行为：2025年03月18日，我局接群众举报后组织人员到现场进行检查。我局专卖执法人员叶辉明(19090352015)、朱兆强(19090352023)依法对位于广东省惠州市博罗县龙溪街道宫庭村龙桥大道1239号\"博罗县龙溪隆胜轩茶烟酒商行\"的经营场所进行检查，在该场所内发现涉嫌违法的烟草专卖品黄果树(长征)200条、白沙(硬精品三代)150条、红塔山(硬经典)150条等17个品种合计1075条，经查证均未在当地烟草专卖批发企业进货。");

        // 证据材料
        mockData.put("evidence_materials", "以上事实有以下证据予以证实：1、涉案卷烟共17个品牌规格合计1075条；2、《证据先行登记保存通知书》1份；3、证据复制（提取）单1份；4、《询问笔录》1份；5、《现场笔录》1份；6、《卷烟、雪茄烟鉴别检验报告》1份；7、《涉案物品核价表》1份。");

        // 违法性质认定
        mockData.put("violation_nature", "你（单位）的上述行为违反了《中华人民共和国烟草专卖法实施条例》第二十三条第二款\"取得烟草专卖零售许可证的企业或者个人，应当在当地烟草专卖批发企业进货\"的规定，构成未在当地烟草专卖批发企业进货的违法行为。");

        // 拟处罚决定
        mockData.put("proposed_punishment", "依据《中华人民共和国烟草专卖法实施条例》第五十六条\"取得烟草专卖零售许可证的企业或者个人违反本条例第二十三条第二款的规定，未在当地烟草专卖批发企业进货的，由烟草专卖行政主管部门没收违法所得，可处以进货总额5%以上10%以下的罚款\"的规定，并遵照《广东省烟草专卖行政处罚裁量权管理办法》相关规定，我局拟对你（单位）作出如下行政处罚：处以未在当地烟草专卖批发企业进货总额人民币108625.00元的9.5％罚款，计罚款人民币10319.37元。");

        // 权利告知
        mockData.put("rights_notice", "根据《中华人民共和国行政处罚法》第四十四条的规定，你（单位）享有以下权利：\n1、有权进行陈述和申辩；\n2、有权要求举行听证。\n如果你（单位）要求举行听证，应当在收到本告知书之日起五日内向我局提出书面申请。逾期不提出听证申请的，视为放弃听证权利。\n如果你（单位）既不申请听证，也不进行陈述、申辩，我局将依法作出行政处罚决定。");

        // 联系方式
        mockData.put("contact_info", "联系地址：广东省惠州市博罗县罗阳镇博惠路52号\n联系电话：0752-6212345\n邮政编码：516100");

        // 送达信息
        mockData.put("delivery_method", "直接送达");
        mockData.put("delivery_date", "2025/6/9");
        mockData.put("receiver_name", "梁俊强");
        mockData.put("receiver_signature", "");

        // 执法人员
        mockData.put("officer1_name", "叶辉明");
        mockData.put("officer1_id", "19090352015");
        mockData.put("officer2_name", "朱兆强");
        mockData.put("officer2_id", "19090352023");

        // 系统字段
        mockData.put("is_active", 1);
        mockData.put("creator", "叶辉明");
        mockData.put("create_time", "2025/6/9 10:30");
        mockData.put("modifier", "叶辉明");
        mockData.put("modify_time", "2025/6/9 10:30");
        mockData.put("own_dept_uuid", "4413231030000002829");
        mockData.put("own_org_uuid", "4413231030000000540");

        return mockData;
    }
}
