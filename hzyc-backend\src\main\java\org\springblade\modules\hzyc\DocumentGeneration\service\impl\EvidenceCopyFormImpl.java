package org.springblade.modules.hzyc.DocumentGeneration.service.impl;

import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONArray;
import org.springblade.modules.hzyc.DocumentGeneration.service.DocumentGenerator;
import org.springblade.modules.hzyc.DocumentGeneration.service.ICaseInfoService;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import org.springblade.modules.hzyc.document.service.IDocumentService;
import org.springblade.modules.hzyc.document.pojo.entity.DocumentEntity;
import org.springblade.core.tool.jackson.JsonUtil;
import org.springframework.beans.factory.annotation.Autowired;

/**
 * 证据复制（提取）单文档生成实现类
 *
 * <AUTHOR>
 */
@Service("evidenceCopyFormDocument")
public class EvidenceCopyFormImpl implements DocumentGenerator {
    @Autowired
    private IDocumentService documentService;
	@Autowired
	private ICaseInfoService icaseInfoService;

    @Override
    public List<Map<String, Object>> processData(Map<String, Object> rawData, String caseId, String mode, String fileId) {
        Map<String, Object> processedData = new HashMap<>(rawData);
        // 如果mode为update，从数据库读取已保存的数据
        if ("update".equals(mode) && caseId != null) {
            DocumentEntity existingDoc = documentService.getById(fileId);
            if (existingDoc != null && existingDoc.getDocumentContent() != null) {
                // 从JSON字符串解析为Map
                processedData = JsonUtil.readMap(existingDoc.getDocumentContent());
                return List.of(processedData);
            }
        }

        // 如果没有找到已保存的数据或mode不是update，使用模拟数据
		processedData = StrUtil.isNotBlank(caseId) ? getData(caseId) : getMockData();
        return List.of(processedData);
    }

    @Override
    public String getTemplateName() {
        return "17证据复制（提取）单.docx";
    }

    @Override
    public String getDocumentType() {
        return "EVIDENCE-EXTRACTION-FORM";
    }

	private Map<String, Object> getData(String caseId) {
		Map<String, Object> data = new HashMap<>();
		Map<String, Object> query=new HashMap<>();
		query.put("AJBS", caseId);
		JSONArray array = icaseInfoService.getCaseEvidenceCopyDailyReport(query);
		if(array != null && !array.isEmpty()) {
			Map<String, Object> firstData = (Map<String, Object>) array.get(0);
			Map<String, String> mapper = getTableFieldMapping();
			if(firstData != null) {
				// 处理数据
				firstData.forEach((key, value) -> {
					String newKey = mapper.get(key);
					if (StrUtil.isBlank(newKey)) {
						newKey = key;
					}
					data.put(newKey, value);
				});
				System.out.println(data);
				return data;
			}
		}
		return data;
	}

	public static Map<String, String> getTableFieldMapping() {
		Map<String, String> fieldMapping = new HashMap<>();

		fieldMapping.put("TQDD", "get_address");
		fieldMapping.put("ZFRYJZFZH", "le_person");
		fieldMapping.put("DSR", "party");
		fieldMapping.put("CJR", "creator");
		fieldMapping.put("WSHZJLX", "full_doc_no");
		fieldMapping.put("AJBS", "case_uuid");
		fieldMapping.put("CBBMUUID", "reg_dept_uuid");
		fieldMapping.put("XGSJ", "modify_time");
		fieldMapping.put("FWZXZDTBSJYFWZXSJG", "sysupdatetime");
		fieldMapping.put("TGR", "provider");
		fieldMapping.put("SMSX", "explain_item");
		fieldMapping.put("XYWYBS", "tid");
		fieldMapping.put("ZJFZBS", "evid_copy_uuid");
		fieldMapping.put("XTCJSJCXBYDX", "sys_create_time");
		fieldMapping.put("KZZD1", "ext1");
		fieldMapping.put("SJSSDWYYTYSJQXCL", "own_org_uuid");
		fieldMapping.put("TAR", "same_party");
		fieldMapping.put("TZZD", "ext_json");
		fieldMapping.put("SJMC", "city_org_name");
		fieldMapping.put("SFYX", "is_active");
		fieldMapping.put("DWJC", "org_short_name");
		fieldMapping.put("MCRKSJ", "mc_tec_ctime");
		fieldMapping.put("KZZD2", "ext2");
		fieldMapping.put("TGRDW", "provider_org");
		fieldMapping.put("ZJLX", "evid_type");
		fieldMapping.put("XGR", "modifier");
		fieldMapping.put("JGSXZ", "org_abbr");
		fieldMapping.put("WSSJ", "doc_date");
		fieldMapping.put("XTGXSJCXBYDX", "sys_modify_time");
		fieldMapping.put("BZ", "remark");
		fieldMapping.put("CJSJ", "create_time");
		fieldMapping.put("SJBM", "city_org_code");
		fieldMapping.put("AJMC", "case_name");
		fieldMapping.put("ZZJGUUID", "org_uuid");
		fieldMapping.put("KZZD3", "ext3");
		fieldMapping.put("SJSSBMYYTYSJQXCL", "own_dept_uuid");
		fieldMapping.put("CHBMUUID", "get_dept_uuid");
		fieldMapping.put("AJBH", "case_code");
		fieldMapping.put("FWZXZDTBSJYFWZXSJS", "sysisdelete");
		fieldMapping.put("TQSJ", "get_time");
		fieldMapping.put("ZFRYUUIDS", "le_person_uuids");

		return fieldMapping;
	}

    /**
     * 获取模拟数据
     */
    private Map<String, Object> getMockData() {
        Map<String, Object> mockData = new HashMap<>();

        // 基础信息
        mockData.put("extraction_uuid", "51f8b0721d3c43fdb0adbc151c3a1489");
        mockData.put("case_uuid", "6358d676d24647f1b824c1f362674299");
        mockData.put("org_shortname", "广东省博罗县烟草专卖局");
        mockData.put("full_doc_no", "博烟提﹝2025﹞第48号");
        mockData.put("doc_date", "2025年6月10日");

        // 案件信息
        mockData.put("case_name", "梁俊强涉嫌违法经营卷烟案");
        mockData.put("case_code", "博烟案﹝2025﹞第48号");

        // 当事人信息
        mockData.put("party_name", "梁俊强");
        mockData.put("party_info", "当事人：梁俊强，字号：博罗县龙溪隆胜轩茶烟酒商行，身份证号码：441322199203166034，经营地址：广东省博罗县龙溪街道宫庭村龙桥大道1239号");

        // 提取信息
        mockData.put("extraction_time", "2025年03月18日");
        mockData.put("extraction_location", "广东省惠州市博罗县龙溪街道宫庭村龙桥大道1239号");
        mockData.put("extraction_reason", "案件调查需要，提取相关证据材料");
        mockData.put("undertaker", "叶辉明,朱兆强");
        mockData.put("undertaker_insp_no", "19090352015,19090352023");

        // 证据清单
        List<Map<String, Object>> evidenceItems = new ArrayList<>();

        Map<String, Object> item1 = new HashMap<>();
        item1.put("order_index", 1);
        item1.put("evidence_type", "身份证复印件");
        item1.put("evidence_name", "梁俊强身份证复印件");
        item1.put("evidence_qty", 1);
        item1.put("evidence_unit", "份");
        item1.put("extraction_method", "复制");
        item1.put("storage_location", "案卷第1页");
        item1.put("memo", "身份证正反面复印件");
        evidenceItems.add(item1);

        Map<String, Object> item2 = new HashMap<>();
        item2.put("order_index", 2);
        item2.put("evidence_type", "许可证复印件");
        item2.put("evidence_name", "烟草专卖零售许可证复印件");
        item2.put("evidence_qty", 1);
        item2.put("evidence_unit", "份");
        item2.put("extraction_method", "复制");
        item2.put("storage_location", "案卷第2页");
        item2.put("memo", "许可证号：441322113031");
        evidenceItems.add(item2);

        Map<String, Object> item3 = new HashMap<>();
        item3.put("order_index", 3);
        item3.put("evidence_type", "现场照片");
        item3.put("evidence_name", "涉案店铺及卷烟照片");
        item3.put("evidence_qty", 8);
        item3.put("evidence_unit", "张");
        item3.put("extraction_method", "拍摄");
        item3.put("storage_location", "案卷第3-10页");
        item3.put("memo", "包含店铺外观、内部陈列、涉案卷烟等照片");
        evidenceItems.add(item3);

        Map<String, Object> item4 = new HashMap<>();
        item4.put("order_index", 4);
        item4.put("evidence_type", "电子数据");
        item4.put("evidence_name", "涉案卷烟32位码详情单");
        item4.put("evidence_qty", 1);
        item4.put("evidence_unit", "份");
        item4.put("extraction_method", "打印");
        item4.put("storage_location", "案卷第11页");
        item4.put("memo", "通过扫码获取的卷烟信息");
        evidenceItems.add(item4);

        mockData.put("evidence_items", evidenceItems);

        // 统计信息
        mockData.put("total_evidence_count", 4);
        mockData.put("total_pages", 11);

        // 提取方式说明
        mockData.put("extraction_method_desc", "采用复制、拍摄、打印等方式提取证据，确保证据的完整性和真实性");

        // 法律依据
        mockData.put("legal_basis", "《中华人民共和国行政处罚法》第三十七条、《烟草专卖行政处罚程序规定》第十八条");

        // 当事人确认
        mockData.put("party_signature", "梁俊强");
        mockData.put("party_signature_time", "2025年03月18日");
        mockData.put("party_opinion", "对提取的证据无异议");

        // 执法人员
        mockData.put("executor1_name", "叶辉明");
        mockData.put("executor1_insp_no", "19090352015");
        mockData.put("executor2_name", "朱兆强");
        mockData.put("executor2_insp_no", "19090352023");

        // 系统字段
        mockData.put("is_active", 1);
        mockData.put("creator", "蔡秋宝");
        mockData.put("create_time", "2025/6/10 17:15");
        mockData.put("modifier", "蔡秋宝");
        mockData.put("modify_time", "2025/6/10 17:15");
        mockData.put("city_org_code", "10441300");
        mockData.put("city_org_name", "惠州市");

        return mockData;
    }
}
