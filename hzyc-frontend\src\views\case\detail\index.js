// 文档组件映射表
const documentComponents = {
  '举报记录表': () => import('./ReportRecord.vue'),
  '立案报告表': () => import('./CaseFilingReport.vue'),
  '不予立案报告表': () => import('./NonFilingReport.vue'),
  '不予立案告知书': () => import('./NonFilingNotice.vue'),
  '延长立案期限审批表': () => import('./ExtendFilingApproval.vue'),
  '延长立案期限告知书': () => import('./ExtendFilingNotice.vue'),
  '指定管辖通知书': () => import('./JurisdictionNotice.vue'),
  '现场笔录': () => import('./SceneRecord.vue'),
  '电子数据证据提取笔录': () => import('./ElectronicDataRecord.vue'),
  '证据先行登记保存批准书': () => import('./EvidencePreservationApproval.vue'),
  '证据先行登记保存通知书': () => import('./EvidencePreservationNotice.vue'),
  '先行登记保存证据处理通知书': () => import('./EvidenceHandlingNotice.vue'),
  '抽样取证物品清单': () => import('./SamplingList.vue'),
  '违法物品变价处理审批表': () => import('./IllegalGoodsValuationApproval.vue'),
  '违法物品移交单': () => import('./IllegalGoodsTransferForm.vue'),
  '销毁记录表': () => import('./DestructionRecord.vue'),
  '行政强制执行事项审批表': () => import('./EnforcementApproval.vue'),
  '行政处罚强制执行申请书': () => import('./EnforcementApplication.vue'),
  '加处罚款决定书': () => import('./AdditionalFineDecision.vue'),
  '行政处罚决定履行催告书': () => import('./PenaltyReminderNotice.vue'),
  '延期（分期）缴纳罚款审批表': () => import('./FinePaymentExtensionApproval.vue'),
  '延期（分期）缴纳罚款通知书': () => import('./FinePaymentExtensionNotice.vue'),
  '对协助办案有功个人、单位授奖呈报表': () => import('./AwardReportForm.vue'),
  '撤销立案报告表': () => import('./CaseWithdrawalReport.vue'),
  '撤销立案通知书': () => import('./CaseWithdrawalNotice.vue'),
  '结案报告表': () => import('./CaseClosureReport.vue'),
  '卷宗封面': () => import('./CaseCover.vue'),
  '卷宗目录': () => import('./CaseDirectory.vue'),
  '卷内备考表': () => import('./CaseReferenceForm.vue'),
  '行政处罚决定书': () => import('./PenaltyDecision.vue'),
  '送达回证': () => import('./DeliveryReceipt.vue'),
  '行政处理决定书': () => import('./AdministrativeDecision.vue'),
  '询问笔录': () => import('./InquiryRecord.vue'),
  '证据复制（提取）单': () => import('./EvidenceCopyForm.vue'),
  '行政处罚事先告知书': () => import('./PenaltyNotice.vue'),
  '案件调查终结报告': () => import('./InvestigationReport.vue'),
  '案件处理审批表': () => import('./CaseApprovalForm.vue')
  
}

export default documentComponents




