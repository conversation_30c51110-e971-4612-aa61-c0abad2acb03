<template>
  <div class="evidence-copy-form-container">
    <!-- 文档头部操作栏 -->
    <div class="document-header">
      <div class="header-left">
        <h3>证据复制（提取）单</h3>
      </div>
      <div class="header-right">
        <el-button
          v-if="!isEditing"
          size="small"
          type="primary"
          @click="toggleEdit"
          icon="Edit">
          编辑
        </el-button>
        <template v-else>
          <el-button
            size="small"
            type="success"
            @click="saveEdit"
            icon="Check">
            保存
          </el-button>
          <el-button
            size="small"
            type="info"
            @click="cancelEdit"
            icon="Close">
            取消
          </el-button>
        </template>
        <el-button
          size="small"
          @click="downloadOriginalFile"
          icon="Download">
          导出
        </el-button>
      </div>
    </div>

    <!-- 直接显示DOCX预览，不使用弹窗 -->
    <div class="document-preview" v-if="props.fileUrl&&!isEditing">
      <div class="preview-content" v-loading="previewLoading">
        <VueOfficeDocx
          :src="props.fileUrl"
          style="width: 100%;"
          @rendered="onDocxRendered"
          @error="onPreviewError"
        />
      </div>
    </div>

    <!-- 如果没有文件URL或不是DOCX，显示表单编辑模式 -->
    <div v-else class="document-body">
      <template v-if="isEditing">
        <div class="document-layout">
          <!-- 文档头部 -->
          <div class="document-header-section">
            <div class="org-name">
              <el-input
                v-model="formData.org_short_name"
                placeholder="机构简称"
                class="org-input"
              />
            </div>

            <div class="document-title">
              <h2>证据复制（提取）单</h2>
            </div>
          </div>

          <!-- 证据粘贴处 -->
          <div class="content-section">
            <div class="evidence-box">
              <div class="evidence-label">（证据粘贴处）</div>
              <div class="textarea-wrapper">
                <el-input
                  v-model="formData.evidence_content"
                  type="textarea"
                  :autosize="{ minRows: 8 }"
                  placeholder="证据粘贴处"
                  class="evidence-content auto-resize-textarea"
                  maxlength="2000"
                  show-word-limit
                />
              </div>
            </div>
          </div>

          <!-- 说明事项 -->
          <div class="content-section">
            <div class="section-label">说明事项：</div>
            <div class="textarea-wrapper">
              <el-input
                v-model="formData.explain_item"
                type="textarea"
                :autosize="{ minRows: 3 }"
                placeholder="说明事项"
                class="explain-item auto-resize-textarea"
                maxlength="1000"
                show-word-limit
              />
            </div>
          </div>

          <!-- 复制（提取）地点 -->
          <div class="content-section inline-section">
            <span class="inline-label">复制（提取）地点：</span>
            <el-input
              v-model="formData.get_address"
              placeholder="复制（提取）地点"
              class="inline-input"
            />
          </div>

          <!-- 复制（提取）时间 -->
          <div class="content-section inline-section">
            <span class="inline-label">复制（提取）时间：</span>
            <el-input
              v-model="formData.get_time"
              placeholder="复制（提取）时间"
              class="inline-input"
            />
          </div>

          <!-- 执法人员及执法证号 -->
          <div class="content-section">
            <div class="section-label">执法人员及执法证号：</div>
            <div class="textarea-wrapper">
              <el-input
                v-model="formData.le_person"
                type="textarea"
                :autosize="{ minRows: 2 }"
                placeholder="执法人员及执法证号"
                class="le-person auto-resize-textarea"
                maxlength="500"
                show-word-limit
              />
            </div>
          </div>
        </div>
      </template>
    </div>
  </div>
</template>

<script setup>
import { ref, watch, onMounted, onUnmounted } from 'vue'
import { ElMessage } from 'element-plus'


import VueOfficeDocx from '@vue-office/docx'
// 在组件中导入样式
import '@/styles/vue-office-docx.css'
import '@/styles/document-common.scss';

const props = defineProps({
  documentData: {
    type: Object,
    default: () => ({})
  },
  fileUrl: {
    type: String,
    default: ''
  },
  fileType: {
    type: String,
    default: ''
  },
  fileName: {
    type: String,
    default: ''
  }
})

// 添加调试日志
watch(() => props.fileUrl, (newVal, oldVal) => {
  console.log('fileUrl changed:', { newVal, oldVal })
}, { immediate: true })

watch(() => props, (newVal) => {
  console.log('所有props:', newVal)
}, { immediate: true, deep: true })

const emit = defineEmits(['save'])

// 编辑状态
const isEditing = ref(false)

// 表单数据
const formData = ref({
  org_short_name: '',
  evidence_content: '',
  explain_item: '',
  get_address: '',
  get_time: '',
  le_person: ''
})

// 预览相关状态
const previewLoading = ref(false)

// 保存原始数据的备份
const originalFormData = ref({})

// 监听传入的文档数据
watch(() => props.documentData, (newVal) => {
  if (newVal && Object.keys(newVal).length > 0) {
    // 从documentContent中提取属性，先解析JSON字符串
    let docContent = {}
    try {
      if (typeof newVal.documentContent === 'string') {
        docContent = JSON.parse(newVal.documentContent)
      } else {
        docContent = newVal.documentContent || {}
      }
    } catch (error) {
      docContent = {}
    }

    // 合并数据，优先使用documentContent中的数据
    formData.value = {
      ...formData.value,
      ...newVal,
      // 从documentContent中提取具体字段
      org_short_name: docContent.org_short_name || docContent.orgShortName || newVal.org_short_name || '',
      evidence_content: docContent.evidence_content || docContent.evidenceContent || newVal.evidence_content || '',
      explain_item: docContent.explain_item || docContent.explainItem || newVal.explain_item || '',
      get_address: docContent.get_address || docContent.getAddress || newVal.get_address || '',
      get_time: docContent.get_time || docContent.getTime || newVal.get_time || '',
      le_person: docContent.le_person || docContent.lePerson || newVal.le_person || ''
    }

    // 保存原始数据的备份
    originalFormData.value = { ...formData.value }
  }
}, { immediate: true, deep: true })

// 修改切换编辑状态函数
const toggleEdit = () => {
  if (!isEditing.value) {
    // 进入编辑模式时保存当前数据作为备份
    originalFormData.value = { ...formData.value }
  }
  isEditing.value = !isEditing.value
}

// 新增保存编辑函数
const saveEdit = () => {
  // 保存时只传递需要的数据字段，避免传递整个formData
  const saveData = {
    org_short_name: formData.value.org_short_name,
    evidence_content: formData.value.evidence_content,
    explain_item: formData.value.explain_item,
    get_address: formData.value.get_address,
    get_time: formData.value.get_time,
    le_person: formData.value.le_person
  }

  emit('save', saveData)
  isEditing.value = false
}

// 新增取消编辑函数
const cancelEdit = () => {
  // 恢复原始数据
  formData.value = { ...originalFormData.value }
  isEditing.value = false
  ElMessage.info('已取消编辑，数据已恢复')
}

// 文档渲染完成回调
const onDocxRendered = () => {
  previewLoading.value = false
}

// 预览错误处理
const onPreviewError = (error) => {
  previewLoading.value = false
  console.error('文档预览失败:', error)
  ElMessage.error('文档预览失败，请检查文件格式或网络连接')
}

// 下载原文件
const downloadOriginalFile = () => {
  if (props.fileUrl) {
    const link = document.createElement('a')
    link.href = props.fileUrl
    link.download = props.fileName || '证据复制（提取）单'
    link.target = '_blank'
    document.body.appendChild(link)
    link.click()
    document.body.removeChild(link)
    ElMessage.success('开始下载原文件')
  } else {
    ElMessage.warning('原文件下载链接不存在')
  }
}

// 在 onMounted 中添加事件监听
onMounted(() => {
  // 监听优化事件
  const handleOptimizeEvent = (event) => {
    const { action } = event.detail
    // 根据不同的优化动作进入编辑模式
    if (action === 'evidenceContent' || action === 'explainItem' || action === 'getAddress') {
      isEditing.value = true
      ElMessage.info('已进入编辑模式，请完善相关信息')
    }
  }

  document.addEventListener('document-optimize', handleOptimizeEvent)

  // 组件卸载时移除监听器
  onUnmounted(() => {
    document.removeEventListener('document-optimize', handleOptimizeEvent)
  })
})
</script>

<style scoped>
/* 组件特有样式已移至 @/styles/document-common.scss */

/* 证据复制（提取）单特有样式 */
.evidence-copy-form-container {
  height: 100%;
  display: flex;
  flex-direction: column;
}

.evidence-box {
  border: 2px solid #333;
  min-height: 200px;
  position: relative;
  margin-bottom: 20px;
}

.evidence-label {
  position: absolute;
  top: -12px;
  left: 20px;
  background: white;
  padding: 0 10px;
  font-size: 14px;
  color: #666;
}

.section-label {
  font-weight: 500;
  color: #333;
  margin-bottom: 10px;
  display: block;
}

.inline-section {
  display: flex;
  align-items: center;
  margin-bottom: 20px;
}

.inline-label {
  font-weight: 500;
  color: #333;
  margin-right: 10px;
  white-space: nowrap;
}

.inline-input {
  flex: 1;
  max-width: 400px;
}

.org-input {
  width: 200px;
}

/* 证据内容区域特殊样式 */
.evidence-content {
  border: none !important;
}

.evidence-content .el-textarea__inner {
  border: none !important;
  box-shadow: none !important;
  padding: 20px;
  min-height: 180px;
  resize: none;
}

.evidence-content .el-textarea__inner:focus {
  border: none !important;
  box-shadow: none !important;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .inline-section {
    flex-direction: column;
    align-items: flex-start;
  }

  .inline-label {
    margin-bottom: 5px;
  }

  .inline-input {
    width: 100%;
    max-width: none;
  }
}
</style>
