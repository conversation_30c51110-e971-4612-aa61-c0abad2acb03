<template>
  <div class="penalty-decision-container">
    <!-- 文档头部操作栏 -->
    <div class="document-header">
      <div class="header-left">
        <h3>案件处理审批表</h3>
      </div>
      <div class="header-right">
        <el-button
          v-if="!isEditing"
          size="small"
          type="primary"
          @click="toggleEdit"
          icon="Edit">
          编辑
        </el-button>
        <template v-else>
          <el-button
            size="small"
            type="success"
            @click="saveEdit"
            icon="Check">
            保存
          </el-button>
          <el-button
            size="small"
            type="info"
            @click="cancelEdit"
            icon="Close">
            取消
          </el-button>
        </template>
        <el-button
          size="small"
          @click="downloadOriginalFile"
          icon="Download">
          导出
        </el-button>
      </div>
    </div>

    <!-- 直接显示DOCX预览，不使用弹窗 -->
    <div class="document-preview" v-if="props.fileUrl && !isEditing">
      <div class="preview-content" v-loading="previewLoading">
        <VueOfficeDocx
          :src="props.fileUrl"
          style="width: 100%;"
          @rendered="onDocxRendered"
          @error="onPreviewError"
        />
      </div>
    </div>

    <!-- 如果没有文件URL或不是DOCX，显示表单编辑模式 -->
    <div v-else class="document-body">
      <template v-if="isEditing">
        <div class="document-layout">
          <!-- 文档头部 -->
          <div class="document-header-section">
            <div class="org-name">
              <el-input
                v-model="formData.org_shortname"
                placeholder="机构简称"
                class="org-input"
              />
            </div>

            <div class="document-title">
              <h2>案件处理审批表</h2>
            </div>
          </div>

          <!-- 案件处理审批表格 -->
          <div class="form-table">
            <table class="approval-table">
              <!-- 案由行 -->
              <tr>
                <td class="label-cell">案由</td>
                <td class="content-cell" colspan="3">
                  <el-input
                    v-model="formData.cause_of_action"
                    placeholder="请输入案由"
                  />
                </td>
              </tr>

              <!-- 案件来源行 -->
              <tr>
                <td class="label-cell">案件来源</td>
                <td class="content-cell" colspan="3">
                  <el-input
                    v-model="formData.case_source"
                    placeholder="请输入案件来源"
                  />
                </td>
              </tr>

              <!-- 立案编号和立案日期行 -->
              <tr>
                <td class="label-cell">立案编号</td>
                <td class="content-cell">
                  <el-input
                    v-model="formData.reg_no"
                    placeholder="请输入立案编号"
                  />
                </td>
                <td class="label-cell">立案日期</td>
                <td class="content-cell">
                  <el-date-picker
                    v-model="formData.reg_date"
                    type="date"
                    placeholder="请选择立案日期"
                    format="YYYY-MM-DD"
                    value-format="YYYY-MM-DD"
                    style="width: 100%;"
                  />
                </td>
              </tr>

              <!-- 当事人信息 -->
              <tr>
                <td class="label-cell party-label" rowspan="6">当<br/>事<br/>人</td>
                <td class="label-cell">单位</td>
                <td class="sub-label-cell">名称</td>
                <td class="content-cell">
                  <el-input
                    v-model="formData.party_name"
                    placeholder="请输入单位名称"
                  />
                </td>
              </tr>

              <tr>
                <td class="label-cell" rowspan="2">单位</td>
                <td class="sub-label-cell">法定代表人<br/>（负责人）</td>
                <td class="content-cell">
                  <el-input
                    v-model="formData.legal_representative"
                    placeholder="请输入法定代表人"
                  />
                </td>
              </tr>

              <tr>
                <td class="sub-label-cell">联系电话</td>
                <td class="content-cell">
                  <el-input
                    v-model="formData.party_phone"
                    placeholder="请输入联系电话"
                  />
                </td>
              </tr>

              <tr>
                <td class="label-cell" rowspan="3">个人（个体<br/>工商户）</td>
                <td class="sub-label-cell">地址</td>
                <td class="content-cell">
                  <el-input
                    v-model="formData.address"
                    placeholder="请输入地址"
                  />
                </td>
              </tr>

              <!-- 个人信息详细行 -->
              <tr>
                <td class="content-cell" colspan="2">
                  <div class="multi-field-row">
                    <span>姓名</span>
                    <el-input
                      v-model="formData.individual_name"
                      placeholder="姓名"
                      style="width: 80px; margin: 0 5px;"
                    />
                    <span>性别</span>
                    <el-select
                      v-model="formData.gender"
                      placeholder="性别"
                      style="width: 60px; margin: 0 5px;"
                    >
                      <el-option label="男" value="男" />
                      <el-option label="女" value="女" />
                    </el-select>
                    <span>年龄</span>
                    <el-input
                      v-model="formData.age"
                      placeholder="年龄"
                      style="width: 60px; margin: 0 5px;"
                    />
                    <span>民族</span>
                    <el-input
                      v-model="formData.ethnicity"
                      placeholder="民族"
                      style="width: 80px; margin: 0 5px;"
                    />
                    <span style="margin-left: 20px;">联系电话</span>
                    <el-input
                      v-model="formData.party_phone"
                      placeholder="联系电话"
                      style="width: 120px; margin-left: 5px;"
                    />
                  </div>
                </td>
              </tr>

              <tr>
                <td class="sub-label-cell">证件类型及号码</td>
                <td class="content-cell">
                  <el-input
                    v-model="formData.id_type"
                    placeholder="请输入证件类型及号码"
                  />
                </td>
              </tr>

              <tr>
                <td class="sub-label-cell">住址</td>
                <td class="content-cell">
                  <el-input
                    v-model="formData.address"
                    placeholder="请输入住址"
                  />
                </td>
              </tr>

              <!-- 同案人 -->
              <tr>
                <td class="label-cell">同案人</td>
                <td class="content-cell" colspan="3">
                  <el-input
                    v-model="formData.co_defendants"
                    type="textarea"
                    :autosize="{ minRows: 2, maxRows: 4 }"
                    placeholder="请输入同案人信息"
                  />
                </td>
              </tr>

              <!-- 违法事实 -->
              <tr>
                <td class="label-cell">违法事实</td>
                <td class="content-cell" colspan="3">
                  <el-input
                    v-model="formData.case_fact"
                    type="textarea"
                    :autosize="{ minRows: 3, maxRows: 6 }"
                    placeholder="请输入违法事实"
                  />
                </td>
              </tr>

              <!-- 处罚（处理）依据 -->
              <tr>
                <td class="label-cell">处罚（处理）依据</td>
                <td class="content-cell" colspan="3">
                  <el-input
                    v-model="formData.punish_argument"
                    type="textarea"
                    :autosize="{ minRows: 3, maxRows: 6 }"
                    placeholder="请输入处罚（处理）依据"
                  />
                </td>
              </tr>

              <!-- 承办人意见 -->
              <tr>
                <td class="label-cell">承办人意见</td>
                <td class="content-cell" colspan="3">
                  <el-input
                    v-model="formData.handle_person_advice"
                    type="textarea"
                    :autosize="{ minRows: 4, maxRows: 8 }"
                    placeholder="请输入承办人意见"
                  />
                  <div class="signature-row" style="margin-top: 10px;">
                    <span>签名：</span>
                    <el-input
                      v-model="formData.handler_signature"
                      placeholder="承办人签名"
                      style="width: 120px; margin: 0 10px;"
                    />
                    <el-date-picker
                      v-model="formData.submit_time"
                      type="date"
                      placeholder="签名日期"
                      format="YYYY年MM月DD日"
                      value-format="YYYY-MM-DD"
                      style="width: 150px;"
                    />
                  </div>
                </td>
              </tr>

              <!-- 承办部门意见 -->
              <tr>
                <td class="label-cell">承办部门意见</td>
                <td class="content-cell" colspan="3">
                  <el-input
                    v-model="formData.department_opinion"
                    type="textarea"
                    :autosize="{ minRows: 4, maxRows: 8 }"
                    placeholder="请输入承办部门意见"
                  />
                  <div class="signature-row" style="margin-top: 10px;">
                    <span>签名：</span>
                    <el-input
                      v-model="formData.department_signature"
                      placeholder="部门负责人签名"
                      style="width: 120px; margin: 0 10px;"
                    />
                    <span>年　月　日</span>
                  </div>
                </td>
              </tr>

              <!-- 法制部门意见 -->
              <tr>
                <td class="label-cell">法制部门意见</td>
                <td class="content-cell" colspan="3">
                  <el-input
                    v-model="formData.legal_department_opinion"
                    type="textarea"
                    :autosize="{ minRows: 3, maxRows: 6 }"
                    placeholder="请输入法制部门意见"
                  />
                  <div class="signature-row" style="margin-top: 10px;">
                    <span>审核人签名：</span>
                    <el-input
                      v-model="formData.reviewer_signature"
                      placeholder="审核人签名"
                      style="width: 120px; margin: 0 10px;"
                    />
                    <span style="margin-left: 30px;">负责人签名：</span>
                    <el-input
                      v-model="formData.supervisor_signature"
                      placeholder="负责人签名"
                      style="width: 120px; margin: 0 10px;"
                    />
                  </div>
                  <div class="signature-row" style="justify-content: flex-end; margin-top: 10px;">
                    <span>年　月　日</span>
                  </div>
                </td>
              </tr>

              <!-- 负责人意见 -->
              <tr>
                <td class="label-cell">负责人意见</td>
                <td class="content-cell" colspan="3">
                  <el-input
                    v-model="formData.supervisor_opinion"
                    type="textarea"
                    :autosize="{ minRows: 4, maxRows: 8 }"
                    placeholder="请输入负责人意见"
                  />
                  <div class="signature-row" style="margin-top: 10px;">
                    <span>签名：</span>
                    <el-input
                      v-model="formData.final_supervisor_signature"
                      placeholder="负责人签名"
                      style="width: 120px; margin: 0 10px;"
                    />
                    <span>年　月　日</span>
                  </div>
                </td>
              </tr>

              <!-- 备注 -->
              <tr>
                <td class="label-cell">备注</td>
                <td class="content-cell" colspan="3">
                  <el-input
                    v-model="formData.remarks"
                    type="textarea"
                    :autosize="{ minRows: 2, maxRows: 4 }"
                    placeholder="请输入备注"
                  />
                </td>
              </tr>
            </table>
          </div>
        </div>
      </template>
    </div>
  </div>
</template>




<script setup>
import { ref, watch, onMounted, onUnmounted } from 'vue'
import { ElMessage } from 'element-plus'
import { Edit, Check, View, Download, FullScreen, ScaleToOriginal, Close } from '@element-plus/icons-vue'
import VueOfficeDocx from '@vue-office/docx'
// 在组件中导入样式
import '@/styles/vue-office-docx.css'
import '@/styles/document-common.scss';

const props = defineProps({
  documentData: {
    type: Object,
    default: () => ({})
  },
  fileUrl: {
    type: String,
    default: ''
  },
  fileType: {
    type: String,
    default: ''
  },
  fileName: {
    type: String,
    default: ''
  }
})

// 添加调试日志
watch(() => props.fileUrl, (newVal, oldVal) => {
  console.log('fileUrl changed:', { newVal, oldVal })
}, { immediate: true })

watch(() => props, (newVal) => {
  console.log('所有props:', newVal)
}, { immediate: true, deep: true })

const emit = defineEmits(['save'])

// 编辑状态
const isEditing = ref(false)

// 表单数据
const formData = ref({
  org_shortname: '',
  cause_of_action: '',
  case_source: '',
  reg_no: '',
  reg_date: '',
  party_name: '',
  party_phone: '',
  legal_representative: '',
  address: '',
  individual_name: '',
  gender: '',
  age: '',
  ethnicity: '',
  id_type: '',
  co_defendants: '',
  case_fact: '',
  punish_argument: '',
  handle_person_advice: '',
  handler_signature: '',
  submit_time: '',
  department_opinion: '',
  department_signature: '',
  legal_department_opinion: '',
  reviewer_signature: '',
  supervisor_signature: '',
  supervisor_opinion: '',
  final_supervisor_signature: '',
  remarks: ''
})

// 预览相关状态
const previewLoading = ref(false)

// 保存原始数据的备份
const originalFormData = ref({})

// 监听传入的文档数据
watch(() => props.documentData, (newVal) => {
  if (newVal && Object.keys(newVal).length > 0) {
    // 从documentContent中提取属性，先解析JSON字符串
    let docContent = {}
    try {
      if (typeof newVal.documentContent === 'string') {
        docContent = JSON.parse(newVal.documentContent)
      } else {
        docContent = newVal.documentContent || {}
      }
    } catch (error) {
      docContent = {}
    }

    // 合并数据，优先使用documentContent中的数据
    formData.value = {
      ...formData.value,
      ...newVal,
      // 从documentContent中提取具体字段
      org_shortname: docContent.org_shortname || newVal.org_shortname || '',
      cause_of_action: docContent.cause_of_action || newVal.cause_of_action || '',
      case_source: docContent.case_source || newVal.case_source || '',
      reg_no: docContent.reg_no || newVal.reg_no || '',
      reg_date: docContent.reg_date || newVal.reg_date || '',
      party_name: docContent.party_name || newVal.party_name || '',
      party_phone: docContent.party_phone || newVal.party_phone || '',
      legal_representative: docContent.legal_representative || newVal.legal_representative || '',
      address: docContent.address || newVal.address || '',
      individual_name: docContent.individual_name || newVal.individual_name || '',
      gender: docContent.gender || newVal.gender || '',
      age: docContent.age || newVal.age || '',
      ethnicity: docContent.ethnicity || newVal.ethnicity || '',
      id_type: docContent.id_type || newVal.id_type || '',
      co_defendants: docContent.co_defendants || newVal.co_defendants || '',
      case_fact: docContent.case_fact || newVal.case_fact || '',
      punish_argument: docContent.punish_argument || newVal.punish_argument || '',
      handle_person_advice: docContent.handle_person_advice || newVal.handle_person_advice || '',
      handler_signature: docContent.handler_signature || newVal.handler_signature || '',
      submit_time: docContent.submit_time || newVal.submit_time || '',
      department_opinion: docContent.department_opinion || newVal.department_opinion || '',
      department_signature: docContent.department_signature || newVal.department_signature || '',
      legal_department_opinion: docContent.legal_department_opinion || newVal.legal_department_opinion || '',
      reviewer_signature: docContent.reviewer_signature || newVal.reviewer_signature || '',
      supervisor_signature: docContent.supervisor_signature || newVal.supervisor_signature || '',
      supervisor_opinion: docContent.supervisor_opinion || newVal.supervisor_opinion || '',
      final_supervisor_signature: docContent.final_supervisor_signature || newVal.final_supervisor_signature || '',
      remarks: docContent.remarks || newVal.remarks || ''
    }

    // 保存原始数据的备份
    originalFormData.value = { ...formData.value }
  }
}, { immediate: true, deep: true })

// 修改切换编辑状态函数
const toggleEdit = () => {
  if (!isEditing.value) {
    // 进入编辑模式时保存当前数据作为备份
    originalFormData.value = { ...formData.value }
  }
  isEditing.value = !isEditing.value
}

// 新增保存编辑函数
const saveEdit = () => {
  // 保存时只传递需要的数据字段，避免传递整个formData
  const saveData = {
    org_shortname: formData.value.org_shortname,
    cause_of_action: formData.value.cause_of_action,
    case_source: formData.value.case_source,
    reg_no: formData.value.reg_no,
    reg_date: formData.value.reg_date,
    party_name: formData.value.party_name,
    party_phone: formData.value.party_phone,
    legal_representative: formData.value.legal_representative,
    address: formData.value.address,
    individual_name: formData.value.individual_name,
    gender: formData.value.gender,
    age: formData.value.age,
    ethnicity: formData.value.ethnicity,
    id_type: formData.value.id_type,
    co_defendants: formData.value.co_defendants,
    case_fact: formData.value.case_fact,
    punish_argument: formData.value.punish_argument,
    handle_person_advice: formData.value.handle_person_advice,
    handler_signature: formData.value.handler_signature,
    submit_time: formData.value.submit_time,
    department_opinion: formData.value.department_opinion,
    department_signature: formData.value.department_signature,
    legal_department_opinion: formData.value.legal_department_opinion,
    reviewer_signature: formData.value.reviewer_signature,
    supervisor_signature: formData.value.supervisor_signature,
    supervisor_opinion: formData.value.supervisor_opinion,
    final_supervisor_signature: formData.value.final_supervisor_signature,
    remarks: formData.value.remarks
  }

  emit('save', saveData)
  isEditing.value = false
}

// 新增取消编辑函数
const cancelEdit = () => {
  // 恢复原始数据
  formData.value = { ...originalFormData.value }
  isEditing.value = false
  ElMessage.info('已取消编辑，数据已恢复')
}

// 文档渲染完成回调
const onDocxRendered = () => {
  previewLoading.value = false
}

// 预览错误处理
const onPreviewError = (error) => {
  previewLoading.value = false
  console.error('文档预览失败:', error)
  ElMessage.error('文档预览失败，请检查文件格式或网络连接')
}

// 下载原文件
const downloadOriginalFile = () => {
  if (props.fileUrl) {
    const link = document.createElement('a')
    link.href = props.fileUrl
    link.download = props.fileName || '文档'
    link.target = '_blank'
    document.body.appendChild(link)
    link.click()
    document.body.removeChild(link)
    ElMessage.success('开始下载原文件')
  } else {
    ElMessage.warning('原文件下载链接不存在')
  }
}

// 在 onMounted 中添加事件监听
onMounted(() => {
  // 监听优化事件
  const handleOptimizeEvent = (event) => {
    const { action } = event.detail
    // 根据不同的优化动作进入编辑模式
    if (action === 'partyInfo' || action === 'lawBasis' || action === 'penalty') {
      isEditing.value = true
      ElMessage.info('已进入编辑模式，请完善相关信息')
    }
  }

  document.addEventListener('document-optimize', handleOptimizeEvent)

  // 组件卸载时移除监听器
  onUnmounted(() => {
    document.removeEventListener('document-optimize', handleOptimizeEvent)
  })
})
</script>

<style scoped>
/* 组件特有样式已移至 @/styles/document-common.scss */

/* 案件处理审批表特有样式 */
.approval-table {
  width: 100%;
  border-collapse: collapse;
  border: 2px solid #000;
  font-size: 14px;
  margin-bottom: 30px;
}

.approval-table td {
  border: 1px solid #000;
  padding: 8px 12px;
  vertical-align: middle;
  min-height: 40px;
}

.label-cell {
  background-color: #f5f5f5;
  font-weight: bold;
  width: 120px;
  text-align: center;
  vertical-align: middle;
}

.sub-label-cell {
  background-color: #f9f9f9;
  font-weight: bold;
  width: 100px;
  text-align: center;
  vertical-align: middle;
  font-size: 12px;
}

.content-cell {
  background-color: white;
  min-height: 40px;
  padding: 5px 8px;
}

.party-label {
  writing-mode: vertical-lr;
  text-orientation: upright;
  font-size: 16px;
  font-weight: bold;
  width: 60px;
}

.multi-field-row {
  display: flex;
  align-items: center;
  flex-wrap: wrap;
  gap: 5px;
}

.multi-field-row span {
  font-size: 12px;
  white-space: nowrap;
}

.signature-row {
  display: flex;
  align-items: center;
  justify-content: flex-start;
  margin-top: 10px;
  font-size: 14px;
}

.signature-row span {
  margin-right: 10px;
}

/* Element Plus 样式覆盖 */
:deep(.el-input__wrapper) {
  border: 1px dashed #dcdfe6;
  background-color: #fafafa;
}

:deep(.el-textarea__inner) {
  border: 1px dashed #dcdfe6;
  background-color: #fafafa;
}

:deep(.el-input__wrapper:focus),
:deep(.el-textarea__inner:focus) {
  border-color: #409eff;
  background-color: white;
}

:deep(.el-select .el-input__wrapper) {
  border: 1px dashed #dcdfe6;
  background-color: #fafafa;
}

/* 打印样式 */
@media print {
  .approval-table {
    border: 2px solid #000 !important;
  }

  .approval-table td {
    border: 1px solid #000 !important;
  }
}
</style>
